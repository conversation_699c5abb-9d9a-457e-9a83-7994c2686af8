const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const User = require('../models/User');
const Category = require('../models/Category');
const Tag = require('../models/Tag');
const Article = require('../models/Article');

// 连接数据库
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('🍃 MongoDB 连接成功');
  } catch (error) {
    console.error('数据库连接失败:', error);
    process.exit(1);
  }
};

// 初始化管理员用户
const initAdmin = async () => {
  try {
    const existingAdmin = await User.findOne({ role: 'admin' });
    if (existingAdmin) {
      console.log('管理员用户已存在');
      return;
    }

    const admin = new User({
      username: 'root',
      email: '<EMAIL>',
      password: 'szyroot',
      nickname: '博客管理员',
      bio: '这是博客的管理员账户',
      role: 'admin',
      socialLinks: {
        github: 'https://github.com',
        twitter: 'https://twitter.com',
        linkedin: 'https://linkedin.com',
        website: 'https://example.com'
      }
    });

    await admin.save();
    console.log('✅ 管理员用户创建成功');
    console.log('用户名: admin');
    console.log('密码: admin123');
  } catch (error) {
    console.error('创建管理员用户失败:', error);
  }
};

// 初始化分类
const initCategories = async () => {
  try {
    const existingCategories = await Category.countDocuments();
    if (existingCategories > 0) {
      console.log('分类数据已存在');
      return;
    }

    const categories = [
      {
        name: '技术分享',
        description: '分享各种技术文章和教程',
        color: '#3B82F6',
        icon: 'code',
        sort: 1
      },
      {
        name: '生活随笔',
        description: '记录生活中的点点滴滴',
        color: '#10B981',
        icon: 'heart',
        sort: 2
      },
      {
        name: '学习笔记',
        description: '学习过程中的笔记和总结',
        color: '#F59E0B',
        icon: 'book',
        sort: 3
      },
      {
        name: '项目经验',
        description: '项目开发中的经验和总结',
        color: '#EF4444',
        icon: 'briefcase',
        sort: 4
      }
    ];

    await Category.insertMany(categories);
    console.log('✅ 分类数据初始化成功');
  } catch (error) {
    console.error('初始化分类失败:', error);
  }
};

// 初始化标签
const initTags = async () => {
  try {
    const existingTags = await Tag.countDocuments();
    if (existingTags > 0) {
      console.log('标签数据已存在');
      return;
    }

    const tags = [
      { name: 'JavaScript', color: '#F7DF1E' },
      { name: 'Vue.js', color: '#4FC08D' },
      { name: 'React', color: '#61DAFB' },
      { name: 'Node.js', color: '#339933' },
      { name: 'MongoDB', color: '#47A248' },
      { name: 'Express', color: '#000000' },
      { name: 'CSS', color: '#1572B6' },
      { name: 'HTML', color: '#E34F26' },
      { name: '前端开发', color: '#FF6B6B' },
      { name: '后端开发', color: '#4ECDC4' },
      { name: '全栈开发', color: '#45B7D1' },
      { name: '数据库', color: '#96CEB4' },
      { name: 'API设计', color: '#FFEAA7' },
      { name: '性能优化', color: '#DDA0DD' },
      { name: '用户体验', color: '#98D8C8' }
    ];

    await Tag.insertMany(tags);
    console.log('✅ 标签数据初始化成功');
  } catch (error) {
    console.error('初始化标签失败:', error);
  }
};

// 初始化示例文章
const initArticles = async () => {
  try {
    const existingArticles = await Article.countDocuments();
    if (existingArticles > 0) {
      console.log('文章数据已存在');
      return;
    }

    const admin = await User.findOne({ role: 'admin' });
    const categories = await Category.find();
    const tags = await Tag.find();

    if (!admin || categories.length === 0 || tags.length === 0) {
      console.log('缺少必要数据，跳过文章初始化');
      return;
    }

    const articles = [
      {
        title: '欢迎来到我的博客',
        summary: '这是我的第一篇博客文章，欢迎大家来到我的个人博客网站！',
        content: `
          <h2>欢迎来到我的博客</h2>
          <p>大家好！欢迎来到我的个人博客网站。这里我会分享一些技术文章、生活感悟和学习笔记。</p>
          
          <h3>博客特色</h3>
          <ul>
            <li>🎨 精美的界面设计</li>
            <li>⚡ 流畅的动画效果</li>
            <li>📱 完美的响应式布局</li>
            <li>💬 友好的评论系统</li>
            <li>🔍 强大的搜索功能</li>
          </ul>
          
          <h3>技术栈</h3>
          <p>本博客采用现代化的技术栈构建：</p>
          <ul>
            <li><strong>前端</strong>: Vue 3 + Vite + Tailwind CSS + GSAP</li>
            <li><strong>后端</strong>: Node.js + Express + MongoDB</li>
            <li><strong>部署</strong>: Docker + Nginx</li>
          </ul>
          
          <p>希望大家喜欢这个博客，也欢迎留言交流！</p>
        `,
        author: admin._id,
        category: categories[0]._id,
        tags: [tags[0]._id, tags[1]._id, tags[8]._id],
        status: 'published',
        isTop: true,
        publishedAt: new Date(),
        seoTitle: '欢迎来到我的博客 - 个人技术博客',
        seoDescription: '欢迎来到我的个人博客网站，这里分享技术文章、生活感悟和学习笔记',
        seoKeywords: ['博客', '技术', '分享', '欢迎']
      },
      {
        title: 'Vue 3 组合式API详解',
        summary: '深入了解Vue 3的组合式API，掌握现代Vue开发的核心概念和最佳实践。',
        content: `
          <h2>Vue 3 组合式API详解</h2>
          <p>Vue 3引入了组合式API（Composition API），为我们提供了更灵活的代码组织方式。</p>
          
          <h3>什么是组合式API</h3>
          <p>组合式API是一套基于函数的API，让我们能够更好地组织和复用组件逻辑。</p>
          
          <h3>核心概念</h3>
          <h4>1. setup函数</h4>
          <pre><code>
import { ref, reactive } from 'vue'

export default {
  setup() {
    const count = ref(0)
    const state = reactive({ name: 'Vue 3' })
    
    return {
      count,
      state
    }
  }
}
          </code></pre>
          
          <h4>2. 响应式引用</h4>
          <p>使用ref和reactive创建响应式数据。</p>
          
          <h4>3. 计算属性和侦听器</h4>
          <p>使用computed和watch处理数据变化。</p>
          
          <h3>优势</h3>
          <ul>
            <li>更好的TypeScript支持</li>
            <li>更灵活的逻辑复用</li>
            <li>更清晰的代码组织</li>
          </ul>
        `,
        author: admin._id,
        category: categories[0]._id,
        tags: [tags[0]._id, tags[1]._id, tags[8]._id],
        status: 'published',
        publishedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1天前
        seoTitle: 'Vue 3 组合式API详解 - 现代Vue开发指南',
        seoDescription: '深入了解Vue 3的组合式API，掌握现代Vue开发的核心概念和最佳实践',
        seoKeywords: ['Vue 3', '组合式API', 'Composition API', '前端开发']
      }
    ];

    await Article.insertMany(articles);
    
    // 更新分类和标签的文章数量
    for (const category of categories) {
      await category.updateArticleCount();
    }
    
    for (const tag of tags) {
      await tag.updateArticleCount();
    }

    console.log('✅ 示例文章初始化成功');
  } catch (error) {
    console.error('初始化文章失败:', error);
  }
};

// 主初始化函数
const initDatabase = async () => {
  try {
    await connectDB();
    
    console.log('🚀 开始初始化数据库...');
    
    await initAdmin();
    await initCategories();
    await initTags();
    await initArticles();
    
    console.log('✅ 数据库初始化完成！');
    console.log('\n📝 默认管理员账户:');
    console.log('用户名: admin');
    console.log('密码: admin123');
    console.log('邮箱: <EMAIL>');
    
    process.exit(0);
  } catch (error) {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  }
};

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase();
}

module.exports = {
  initDatabase,
  initAdmin,
  initCategories,
  initTags,
  initArticles
};
